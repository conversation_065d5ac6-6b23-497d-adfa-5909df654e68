from uuid import UUID

from pydantic import BaseModel

from onyx.db.models import UserGroup as UserGroupModel
from onyx.auth.schemas import UserRole
from onyx.server.documents.models import ConnectorCredentialPairDescriptor
from onyx.server.documents.models import Connect<PERSON><PERSON><PERSON>shot
from onyx.server.documents.models import CredentialSnapshot
from onyx.server.features.document_set.models import DocumentSet
from onyx.server.features.persona.models import PersonaSnapshot
from onyx.server.manage.models import UserInfo
from onyx.server.manage.models import UserPreferences


class UserTeamInfo(BaseModel):
    id: str
    email: str
    is_active: bool
    is_superuser: bool
    is_verified: bool
    role: UserRole  # Global role
    team_role: UserRole | None  # Role within this specific team
    preferences: UserPreferences
    current_team_id: int | None = None


class UserTeams(BaseModel):
    id: int
    name: str
    users: list[UserTeamInfo]
    is_up_to_date: bool
    is_up_for_deletion: bool

    @classmethod
    def from_model(cls, user_group_model: UserGroupModel) -> "UserTeams":
        users_with_team_roles = []

        for user in user_group_model.users:
            # Find the user's role in this specific team
            team_role = None
            for rel in user_group_model.user_group_relationships:
                if rel.user_id == user.id:
                    team_role = rel.role
                    break

            users_with_team_roles.append(
                UserTeamInfo(
                    id=str(user.id),
                    email=user.email,
                    is_active=user.is_active,
                    is_superuser=user.is_superuser,
                    is_verified=user.is_verified,
                    role=user.role,  # Global role
                    team_role=team_role,  # Role within this team
                    preferences=UserPreferences(
                        default_model=user.default_model,
                        chosen_assistants=user.chosen_assistants,
                    ),
                    current_team_id=user.current_team_id,
                )
            )

        return cls(
            id=user_group_model.id,
            name=user_group_model.name,
            users=users_with_team_roles,
            is_up_to_date=user_group_model.is_up_to_date,
            is_up_for_deletion=user_group_model.is_up_for_deletion,
        )


class UserTeamMember(BaseModel):
    user_id: UUID
    role: UserRole = UserRole.BASIC  # Default role for team members


class CreateUserTeams(BaseModel):
    name: str
    user_ids: list[UUID] | None = None  # For backward compatibility
    members: list[UserTeamMember] | None = None  # New way with roles


class UpdateUserTeams(BaseModel):
    name: str
    user_ids: list[UUID] | None = None  # For backward compatibility
    members: list[UserTeamMember] | None = None  # New way with roles
