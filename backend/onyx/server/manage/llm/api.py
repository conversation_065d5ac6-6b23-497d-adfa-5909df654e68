from collections.abc import Callable

from fastapi import APIRouter
from fastapi import Depends
from fastapi import HTTPException
from fastapi import Query
from sqlalchemy.orm import Session

from onyx.auth.users import current_admin_user
from onyx.auth.users import current_chat_accesssible_user
from onyx.auth.users import current_team_admin_or_admin_user
from onyx.auth.users import get_current_team_id_from_header
from onyx.auth.users import is_user_team_admin_or_admin
from onyx.db.engine import get_session
from onyx.db.llm import fetch_existing_llm_providers
from onyx.db.llm import fetch_existing_llm_providers_for_user
from onyx.db.llm import fetch_provider
from onyx.db.llm import fetch_provider_by_id
from onyx.db.llm import can_user_edit_llm_provider
from onyx.db.llm import remove_llm_provider
from onyx.db.llm import update_default_provider
from onyx.db.llm import upsert_llm_provider
from onyx.db.models import User
from onyx.auth.schemas import User<PERSON><PERSON>
from onyx.llm.factory import get_default_llms
from onyx.llm.factory import get_llm
from onyx.llm.llm_provider_options import fetch_available_well_known_llms
from onyx.llm.llm_provider_options import WellKnownLLMProviderDescriptor
from onyx.llm.utils import litellm_exception_to_error_msg
from onyx.llm.utils import test_llm
from onyx.server.manage.llm.models import FullLLMProvider
from onyx.server.manage.llm.models import LLMProviderDescriptor
from onyx.server.manage.llm.models import LLMProviderUpsertRequest
from onyx.server.manage.llm.models import TestLLMRequest
from onyx.utils.logger import setup_logger
from onyx.utils.threadpool_concurrency import run_functions_tuples_in_parallel
from onyx.utils.user_teams_validation import validate_user_teams_for_private_resource

logger = setup_logger()

admin_router = APIRouter(prefix="/admin/llm")
basic_router = APIRouter(prefix="/llm")


@admin_router.get("/built-in/options")
def fetch_llm_options(
    _: User | None = Depends(current_team_admin_or_admin_user),
) -> list[WellKnownLLMProviderDescriptor]:
    """Fetch LLM options. Only accessible by admin or team admin users."""
    return fetch_available_well_known_llms()


@admin_router.post("/test")
def test_llm_configuration(
    test_llm_request: TestLLMRequest,
    _: User | None = Depends(current_team_admin_or_admin_user),
) -> None:
    llm = get_llm(
        provider=test_llm_request.provider,
        model=test_llm_request.default_model_name,
        api_key=test_llm_request.api_key,
        api_base=test_llm_request.api_base,
        api_version=test_llm_request.api_version,
        custom_config=test_llm_request.custom_config,
        deployment_name=test_llm_request.deployment_name,
    )

    functions_with_args: list[tuple[Callable, tuple]] = [(test_llm, (llm,))]
    if (
        test_llm_request.fast_default_model_name
        and test_llm_request.fast_default_model_name
        != test_llm_request.default_model_name
    ):
        fast_llm = get_llm(
            provider=test_llm_request.provider,
            model=test_llm_request.fast_default_model_name,
            api_key=test_llm_request.api_key,
            api_base=test_llm_request.api_base,
            api_version=test_llm_request.api_version,
            custom_config=test_llm_request.custom_config,
            deployment_name=test_llm_request.deployment_name,
        )
        functions_with_args.append((test_llm, (fast_llm,)))

    parallel_results = run_functions_tuples_in_parallel(
        functions_with_args, allow_failures=False
    )
    error = parallel_results[0] or (
        parallel_results[1] if len(parallel_results) > 1 else None
    )

    if error:
        client_error_msg = litellm_exception_to_error_msg(
            error, llm, fallback_to_error_msg=True
        )
        raise HTTPException(status_code=400, detail=client_error_msg)


@admin_router.post("/test/default")
def test_default_provider(
    _: User | None = Depends(current_team_admin_or_admin_user),
) -> None:
    try:
        llm, fast_llm = get_default_llms()
    except ValueError:
        logger.exception("Failed to fetch default LLM Provider")
        raise HTTPException(status_code=400, detail="No LLM Provider setup")

    functions_with_args: list[tuple[Callable, tuple]] = [
        (test_llm, (llm,)),
        (test_llm, (fast_llm,)),
    ]
    parallel_results = run_functions_tuples_in_parallel(
        functions_with_args, allow_failures=False
    )
    error = parallel_results[0] or (
        parallel_results[1] if len(parallel_results) > 1 else None
    )
    if error:
        raise HTTPException(status_code=400, detail=error)


@admin_router.get("/provider")
def list_llm_providers(
    user: User | None = Depends(current_team_admin_or_admin_user),
    db_session: Session = Depends(get_session),
) -> list[FullLLMProvider]:
    # Admin users see all providers, team_admin users see only their team's providers + public ones
    if user and user.role == UserRole.ADMIN:
        llm_providers = fetch_existing_llm_providers(db_session)
    else:
        llm_providers = fetch_existing_llm_providers_for_user(db_session, user)

    return [
        FullLLMProvider.from_model(llm_provider_model)
        for llm_provider_model in llm_providers
    ]


@admin_router.put("/provider")
def put_llm_provider(
    llm_provider: LLMProviderUpsertRequest,
    is_creation: bool = Query(
        False,
        description="True if updating an existing provider, False if creating a new one",
    ),
    user: User | None = Depends(current_team_admin_or_admin_user),
    current_team_id: int = Depends(get_current_team_id_from_header),
    db_session: Session = Depends(get_session),
) -> FullLLMProvider:
    # validate request (e.g. if we're intending to create but the name already exists we should throw an error)
    # NOTE: may involve duplicate fetching to Postgres, but we're assuming SQLAlchemy is smart enough to cache
    # the result
    existing_provider = fetch_provider(db_session, llm_provider.name)
    if existing_provider and is_creation:
        raise HTTPException(
            status_code=400,
            detail=f"Cannot create llm with this name.",
        )

    # Check if user can edit this provider (for updates)
    if existing_provider and not is_creation and user:
        if not can_user_edit_llm_provider(user, existing_provider):
            raise HTTPException(
                status_code=403,
                detail="You don't have permission to edit this LLM provider"
            )

    if llm_provider.display_model_names is not None:
        # Ensure default_model_name and fast_default_model_name are in display_model_names
        # This is necessary for custom models and Bedrock/Azure models
        if llm_provider.default_model_name not in llm_provider.display_model_names:
            llm_provider.display_model_names.append(llm_provider.default_model_name)

        if (
            llm_provider.fast_default_model_name
            and llm_provider.fast_default_model_name
            not in llm_provider.display_model_names
        ):
            llm_provider.display_model_names.append(
                llm_provider.fast_default_model_name
            )
    if user.role == UserRole.ADMIN:
        # Validate user_teams for private LLM providers
        validate_user_teams_for_private_resource(
            is_public=llm_provider.is_public,
            user_teams=llm_provider.user_teams,
            resource_type="LLM provider",
            db_session=db_session
        )

    # Apply team_admin restrictions similar to assistants/personas
    if user.role==UserRole.TEAM_ADMIN:

        if llm_provider.is_public:
            raise HTTPException(
                status_code=400,
                detail="You cannot set LLM provider as public"
            )

        # The user must belong to at least one team
        if not current_team_id:
            raise HTTPException(
                status_code=400,
                detail="You must be a member of at least one team to create an LLM provider."
            )

        if llm_provider.user_teams:
            raise HTTPException(
                status_code=400,
                detail="You are not allowed to assign a team to an LLM provider."
            )

        # Automatically assign to user's current team and set as private
        llm_provider.user_teams = [current_team_id]
        llm_provider.is_public = False

    try:
        return upsert_llm_provider(
            llm_provider=llm_provider,
            db_session=db_session,
        )
    except ValueError as e:
        logger.exception("Failed to upsert LLM Provider")
        raise HTTPException(status_code=400, detail=str(e))


@admin_router.delete("/provider/{provider_id}")
def delete_llm_provider(
    provider_id: int,
    user: User | None = Depends(current_team_admin_or_admin_user),
    db_session: Session = Depends(get_session),
) -> None:
    # Check if provider exists
    provider = fetch_provider_by_id(db_session, provider_id)
    if not provider:
        raise HTTPException(status_code=404, detail="LLM Provider not found")

    # Check if user can edit this provider
    if user and not can_user_edit_llm_provider(user, provider):
        raise HTTPException(
            status_code=403,
            detail="You don't have permission to delete this LLM provider"
        )

    remove_llm_provider(db_session, provider_id)


@admin_router.get("/provider/default")
def get_user_default_providers(
    user: User | None = Depends(current_team_admin_or_admin_user),
    current_team_id: int = Depends(get_current_team_id_from_header),
    db_session: Session = Depends(get_session),
) -> dict[str, FullLLMProvider | None]:
    """Get the default LLM provider for the current team."""
    from onyx.db.llm import get_user_team_default_llm_provider

    if not user:
        raise HTTPException(status_code=401, detail="User not authenticated")

    # Get default provider for the current team
    team_defaults = {}
    default_provider_model = get_user_team_default_llm_provider(current_team_id, db_session)
    if default_provider_model:
        team_defaults[str(current_team_id)] = FullLLMProvider.from_model(default_provider_model)
    else:
        team_defaults[str(current_team_id)] = None

    return team_defaults


@admin_router.post("/provider/{provider_id}/default")
def set_provider_as_default(
    provider_id: int,
    user: User | None = Depends(current_team_admin_or_admin_user),
    current_team_id: int = Depends(get_current_team_id_from_header),
    db_session: Session = Depends(get_session),
) -> None:
    from onyx.db.llm import set_user_team_default_llm_provider

    # Check if provider exists
    provider = fetch_provider_by_id(db_session, provider_id)
    if not provider:
        raise HTTPException(status_code=404, detail="LLM Provider not found")

    # For setting as default, team admins can set any provider they have access to
    # (public providers or team-assigned private providers)
    if user and user.role not in [UserRole.ADMIN]:
        # Check if user has access to this provider (public or team-assigned)
        user_providers = fetch_existing_llm_providers_for_user(db_session, user)
        provider_ids = [p.id for p in user_providers]

        if provider_id not in provider_ids:
            raise HTTPException(
                status_code=403,
                detail="You don't have access to this LLM provider"
            )

    if not user:
        raise HTTPException(status_code=401, detail="User not authenticated")

    # Set as default for the current team
    try:
        set_user_team_default_llm_provider(current_team_id, provider_id, db_session)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@admin_router.post("/provider/ensure-team-defaults")
def ensure_all_teams_have_defaults(
    _: User | None = Depends(current_admin_user),
    db_session: Session = Depends(get_session),
) -> dict[str, str]:
    """
    Admin-only endpoint to ensure all active teams have default LLM providers.
    Useful for fixing teams that might be missing defaults.
    """
    from onyx.db.llm import ensure_all_teams_have_default_llm_provider

    try:
        ensure_all_teams_have_default_llm_provider(db_session)
        return {"message": "Successfully ensured all teams have default LLM providers"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to ensure team defaults: {str(e)}")


@admin_router.post("/provider/validate-team-defaults")
def validate_and_fix_team_defaults(
    _: User | None = Depends(current_admin_user),
    db_session: Session = Depends(get_session),
) -> dict[str, int | str]:
    """
    Admin-only endpoint to validate all team default LLM providers and fix any that are invalid.
    This checks that each team's default provider is actually available to that team.
    """
    from onyx.db.llm import validate_and_fix_all_team_defaults

    try:
        summary = validate_and_fix_all_team_defaults(db_session)
        summary["message"] = "Successfully validated and fixed team default LLM providers"
        return summary
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to validate team defaults: {str(e)}")


"""Endpoints for all"""


@basic_router.get("/provider")
def list_llm_provider_basics(
    user: User | None = Depends(current_chat_accesssible_user),
    db_session: Session = Depends(get_session),
) -> list[LLMProviderDescriptor]:
    return [
        LLMProviderDescriptor.from_model(llm_provider_model)
        for llm_provider_model in fetch_existing_llm_providers_for_user(
            db_session, user
        )
    ]
