from sqlalchemy.orm import Session

from onyx.access.access import get_acl_for_user
from onyx.auth.schemas import User<PERSON><PERSON>
from onyx.configs.app_configs import DISABLE_AUTH
from onyx.context.search.models import IndexFilters
from onyx.db.models import User


def build_access_filters_for_user(user: User | None, session: Session, current_team_id: int | None = None) -> list[str] | None:
    # Admin users bypass ACL restrictions entirely
    if (user is None and DISABLE_AUTH) or (user and user.role == UserRole.ADMIN):
        # Return None to indicate no ACL filtering should be applied
        return None

    user_acl = get_acl_for_user(user, session, current_team_id)
    return list(user_acl)


def build_user_only_filters(user: User | None, db_session: Session, current_team_id: int | None = None) -> IndexFilters:
    user_acl_filters = build_access_filters_for_user(user, db_session, current_team_id)
    return IndexFilters(
        source_type=None,
        document_set=None,
        time_cutoff=None,
        tags=None,
        access_control_list=user_acl_filters,  # None for admin users, list[str] for others
    )
