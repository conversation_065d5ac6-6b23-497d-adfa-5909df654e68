"""Utility functions for validating user_teams parameters."""

from fastapi import HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import select


from onyx.db.models import UserGroup, DocumentSet, Tool, DocumentSet__UserGroup, Tool__UserTeams


def validate_user_team_exists(user_team_id: int, db_session: Session) -> None:
    """
    Validates that a user_team exists and is up to date.

    Args:
        user_team_id: ID of the user team to validate
        db_session: Database session

    Raises:
        HTTPException: If user_team does not exist or is not up to date
    """
    user_team = db_session.scalar(
        select(UserGroup).where(
            UserGroup.id == user_team_id,
            UserGroup.is_up_to_date == True  # noqa: E712
        )
    )

    if not user_team:
        raise HTTPException(
            status_code=400,
            detail="user_team does not exist"
        )


def validate_user_teams_single_selection(
    user_teams: list[int] | None,
    field_name: str = "user_teams",
    db_session: Session | None = None
) -> None:
    """
    Validates that exactly one user_team is provided and exists.

    Args:
        user_teams: List of user team IDs
        field_name: Name of the field being validated (for error messages)
        db_session: Database session for existence validation (optional)

    Raises:
        HTTPException: If user_teams is empty, None, contains more than one team, or team doesn't exist
    """

    if not user_teams or len(user_teams) == 0:
        raise HTTPException(
            status_code=400,
            detail=f"Minimum one {field_name} must be assigned"
        )

    if len(user_teams) > 1:
        raise HTTPException(
            status_code=400,
            detail=f"Only one {field_name} is allowed"
        )
    
    # Validate that the user_team exists if db_session is provided
    if db_session is not None:
        validate_user_team_exists(user_teams[0], db_session)


def validate_user_teams_for_private_resource(
    is_public: bool,
    user_teams: list[int] | None,
    resource_type: str = "resource",
    db_session: Session | None = None
) -> None:
    """
    Validates user_teams for private resources.
    For private resources, exactly one user_team must be assigned and must exist.
    For public resources, user_teams should be empty.

    Args:
        is_public: Whether the resource is public
        user_teams: List of user team IDs
        resource_type: Type of resource being validated (for error messages)
        db_session: Database session for existence validation (optional)

    Raises:
        HTTPException: If validation fails
    """
    if is_public:
        if user_teams and len(user_teams) > 0:
            raise HTTPException(
                status_code=400,
                detail=f"Public {resource_type} cannot have user teams assigned"
            )
    else:
        # Private resource - must have exactly one user_team that exists
        validate_user_teams_single_selection(user_teams, "user_team", db_session)


def validate_document_sets_belong_to_team(
    document_set_ids: list[int],
    user_teams: list[int] | None,
    db_session: Session
) -> None:
    """
    Validates that all document sets either belong to the specified team or are public.

    Args:
        document_set_ids: List of document set IDs to validate
        user_teams: List of user team IDs (should contain exactly one team for private personas)
        db_session: Database session

    Raises:
        HTTPException: If any document set doesn't belong to the team and isn't public
    """
    if not document_set_ids:
        return

    if not user_teams or len(user_teams) == 0:
        # For public personas, all document sets must be public
        private_doc_sets = db_session.query(DocumentSet).filter(
            DocumentSet.id.in_(document_set_ids),
            DocumentSet.is_public == False
        ).all()

        if private_doc_sets:
            private_names = [ds.name for ds in private_doc_sets]
            raise HTTPException(
                status_code=400,
                detail=f"Public assistants cannot use private document sets: {', '.join(private_names)}"
            )
        return

    team_id = user_teams[0]

    # Get all document sets and check their accessibility
    doc_sets = db_session.query(DocumentSet).filter(
        DocumentSet.id.in_(document_set_ids)
    ).all()

    invalid_doc_sets = []

    for doc_set in doc_sets:
        if doc_set.is_public:
            # Public document sets are always accessible
            continue

        # Check if the document set belongs to the team
        team_association = db_session.query(DocumentSet__UserGroup).filter(
            DocumentSet__UserGroup.document_set_id == doc_set.id,
            DocumentSet__UserGroup.user_group_id == team_id
        ).first()

        if not team_association:
            invalid_doc_sets.append(doc_set.name)

    if invalid_doc_sets:
        raise HTTPException(
            status_code=400,
            detail=f"The following document sets do not belong to the selected team or are not public: {', '.join(invalid_doc_sets)}"
        )


def validate_tools_belong_to_team(
    tool_ids: list[int],
    user_teams: list[int] | None,
    db_session: Session
) -> None:
    """
    Validates that all tools either belong to the specified team or are public.

    Args:
        tool_ids: List of tool IDs to validate
        user_teams: List of user team IDs (should contain exactly one team for private personas)
        db_session: Database session

    Raises:
        HTTPException: If any tool doesn't belong to the team and isn't public
    """
    if not tool_ids:
        return

    if not user_teams or len(user_teams) == 0:
        # For public personas, all tools must be public
        private_tools = db_session.query(Tool).filter(
            Tool.id.in_(tool_ids),
            Tool.is_public == False
        ).all()

        if private_tools:
            private_names = [tool.name for tool in private_tools]
            raise HTTPException(
                status_code=400,
                detail=f"Public assistants cannot use private tools: {', '.join(private_names)}"
            )
        return

    team_id = user_teams[0]

    # Get all tools and check their accessibility
    tools = db_session.query(Tool).filter(
        Tool.id.in_(tool_ids)
    ).all()

    invalid_tools = []

    for tool in tools:
        if tool.is_public:
            # Public tools are always accessible
            continue

        # Check if the tool belongs to the team
        team_association = db_session.query(Tool__UserTeams).filter(
            Tool__UserTeams.tool_id == tool.id,
            Tool__UserTeams.user_group_id == team_id
        ).first()

        if not team_association:
            invalid_tools.append(tool.name)

    if invalid_tools:
        raise HTTPException(
            status_code=400,
            detail=f"The following tools do not belong to the selected team or are not public: {', '.join(invalid_tools)}"
        )


def filter_document_sets_for_team(
    document_set_ids: list[int],
    user_teams: list[int] | None,
    db_session: Session
) -> list[int]:
    """
    Filters document sets to only include those that belong to the specified team or are public.

    Args:
        document_set_ids: List of document set IDs to filter
        user_teams: List of user team IDs (should contain exactly one team for private personas)
        db_session: Database session

    Returns:
        List of document set IDs that are accessible to the team
    """
    if not document_set_ids:
        return []

    if not user_teams or len(user_teams) == 0:
        # For public personas, only return public document sets
        public_doc_sets = db_session.query(DocumentSet.id).filter(
            DocumentSet.id.in_(document_set_ids),
            DocumentSet.is_public == True
        ).all()

        return [ds.id for ds in public_doc_sets]

    team_id = user_teams[0]

    # Get all document sets and check their accessibility
    doc_sets = db_session.query(DocumentSet).filter(
        DocumentSet.id.in_(document_set_ids)
    ).all()

    accessible_doc_set_ids = []

    for doc_set in doc_sets:
        if doc_set.is_public:
            # Public document sets are always accessible
            accessible_doc_set_ids.append(doc_set.id)
            continue

        # Check if the document set belongs to the team
        team_association = db_session.query(DocumentSet__UserGroup).filter(
            DocumentSet__UserGroup.document_set_id == doc_set.id,
            DocumentSet__UserGroup.user_group_id == team_id
        ).first()

        if team_association:
            accessible_doc_set_ids.append(doc_set.id)

    return accessible_doc_set_ids


def filter_tools_for_team(
    tool_ids: list[int],
    user_teams: list[int] | None,
    db_session: Session
) -> list[int]:
    """
    Filters tools to only include those that belong to the specified team or are public.
    Built-in tools (SearchTool, ImageGenerationTool, InternetSearchTool) are always accessible.

    Args:
        tool_ids: List of tool IDs to filter
        user_teams: List of user team IDs (should contain exactly one team for private personas)
        db_session: Database session

    Returns:
        List of tool IDs that are accessible to the team
    """
    if not tool_ids:
        return []

    # Get all tools and check their accessibility
    tools = db_session.query(Tool).filter(
        Tool.id.in_(tool_ids)
    ).all()

    accessible_tool_ids = []

    for tool in tools:
        # Built-in tools are always accessible regardless of team assignment
        if tool.in_code_tool_id in ["SearchTool", "ImageGenerationTool", "InternetSearchTool"]:
            accessible_tool_ids.append(tool.id)
            continue

        if tool.is_public:
            # Public tools are always accessible
            accessible_tool_ids.append(tool.id)
            continue

        # For private tools, check team assignment only if user_teams is provided
        if user_teams and len(user_teams) > 0:
            # Use the first team ID (for current_team_id this will be the only one)
            team_id = user_teams[0]
            # Check if the tool belongs to the team
            team_association = db_session.query(Tool__UserTeams).filter(
                Tool__UserTeams.tool_id == tool.id,
                Tool__UserTeams.user_group_id == team_id
            ).first()

            if team_association:
                accessible_tool_ids.append(tool.id)

    return accessible_tool_ids
