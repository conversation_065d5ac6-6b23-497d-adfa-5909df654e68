"""Remove old role column from User table

Revision ID: d3e4f5g6h7i8
Revises: c2d3e4f5g6h7
Create Date: 2025-01-13 00:02:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d3e4f5g6h7i8"
down_revision = "c2d3e4f5g6h7"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Remove the old role column from User table
    op.drop_column("user", "role")


def downgrade() -> None:
    # Re-add the role column with the original enum type
    op.add_column(
        "user",
        sa.Column(
            "role",
            sa.Enum(
                "LIMITED",
                "BASIC", 
                "ADMIN",
                "TEAM_ADMIN",
                "CURATOR",
                "GLOBAL_CURATOR",
                "SLACK_USER",
                "EXT_PERM_USER",
                name="userrole",
                native_enum=False,
            ),
            nullable=False,
            server_default="BASIC"
        )
    )
    
    # Restore role data based on the new structure
    op.execute("""
        UPDATE "user" 
        SET role = CASE 
            WHEN is_admin = true AND current_team_id IS NULL THEN 'ADMIN'
            WHEN is_admin = true AND current_team_id IS NOT NULL THEN 'TEAM_ADMIN'
            ELSE 'BASIC'
        END
    """)
    
    # Remove the server default after data restoration
    op.alter_column("user", "role", server_default=None)
