"""Add new columns for user team role migration

Revision ID: b1c2d3e4f5g6
Revises: a1b2c3d4e5f7
Create Date: 2025-01-13 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b1c2d3e4f5g6"
down_revision = "a1b2c3d4e5f7"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add new columns to User table
    op.add_column("user", sa.Column("current_team_id", sa.Integer(), nullable=True))
    op.add_column("user", sa.Column("is_admin", sa.<PERSON>(), nullable=False, server_default=sa.text("false")))
    
    # Add foreign key constraint for current_team_id
    op.create_foreign_key(
        "fk_user_current_team",
        "user",
        "user_group",
        ["current_team_id"],
        ["id"],
        ondelete="SET NULL"
    )
    
    # Add new columns to user__user_group table
    op.add_column("user__user_group", sa.Column("role", sa.String(20), nullable=True))
    op.add_column("user__user_group", sa.Column("is_admin", sa.Boolean(), nullable=False, server_default=sa.text("false")))
    
    # Add check constraint for role enum in user__user_group
    op.create_check_constraint(
        "check_user_group_role",
        "user__user_group",
        "role IN ('ADMIN', 'TEAM_ADMIN', 'BASIC')"
    )
    
    # Create index for current_team_id for better query performance
    op.create_index("ix_user_current_team_id", "user", ["current_team_id"])


def downgrade() -> None:
    # Drop index
    op.drop_index("ix_user_current_team_id", "user")
    
    # Drop check constraint
    op.drop_constraint("check_user_group_role", "user__user_group", type_="check")
    
    # Drop columns from user__user_group table
    op.drop_column("user__user_group", "is_admin")
    op.drop_column("user__user_group", "role")
    
    # Drop foreign key constraint
    op.drop_constraint("fk_user_current_team", "user", type_="foreignkey")
    
    # Drop columns from User table
    op.drop_column("user", "is_admin")
    op.drop_column("user", "current_team_id")
