"""Remove current_team_id column from User table

Revision ID: d3e4f5g6h7i9
Revises: d3e4f5g6h7i8
Create Date: 2025-01-13 00:03:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d3e4f5g6h7i9"
down_revision = "d3e4f5g6h7i8"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Remove the current_team_id column from User table
    op.drop_column("user", "current_team_id")


def downgrade() -> None:
    # Re-add the current_team_id column with foreign key constraint
    op.add_column(
        "user",
        sa.Column(
            "current_team_id",
            sa.Integer(),
            nullable=True
        )
    )
    
    # Re-add the foreign key constraint
    op.create_foreign_key(
        "fk_user_current_team_id_user_group",
        "user",
        "user_group",
        ["current_team_id"],
        ["id"],
        ondelete="SET NULL"
    )
