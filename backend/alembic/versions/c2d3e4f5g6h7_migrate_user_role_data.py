"""Migrate user role data to new structure

Revision ID: c2d3e4f5g6h7
Revises: b1c2d3e4f5g6
Create Date: 2025-01-13 00:01:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c2d3e4f5g6h7"
down_revision = "b1c2d3e4f5g6"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create a special admin group for global admins
    # Using id = -1 as a special identifier for global admin group
    op.execute("""
        INSERT INTO user_group (id, name, is_up_to_date, is_up_for_deletion, time_last_modified_by_user)
        VALUES (-1, 'Global Admins', true, false, NOW())
        ON CONFLICT (id) DO NOTHING
    """)
    
    # Step 1: Populate current_team_id for Team Admin and Basic users
    # Set to the first team they belong to
    op.execute("""
        UPDATE "user" 
        SET current_team_id = (
            SELECT user_group_id 
            FROM user__user_group 
            WHERE user__user_group.user_id = "user".id 
            ORDER BY user_group_id
            LIMIT 1
        )
        WHERE role IN ('TEAM_ADMIN', 'BASIC') 
        AND id IN (SELECT user_id FROM user__user_group WHERE user_group_id > 0)
    """)
    
    # Step 2: Backfill is_admin in User table based on current role
    op.execute("""
        UPDATE "user" 
        SET is_admin = CASE 
            WHEN role = 'ADMIN' THEN true 
            ELSE false 
        END
    """)
    
    # Step 3: Insert admin users into user__user_group with special admin group
    op.execute("""
        INSERT INTO user__user_group (user_id, user_group_id, role, is_admin, is_curator)
        SELECT id, -1, 'ADMIN', true, false
        FROM "user"
        WHERE role = 'ADMIN'
        ON CONFLICT (user_id, user_group_id) DO UPDATE SET
            role = 'ADMIN',
            is_admin = true,
            is_curator = false
    """)
    
    # Step 4: Update existing user__user_group entries with appropriate roles
    op.execute("""
        UPDATE user__user_group
        SET role = CASE
            WHEN u.role = 'TEAM_ADMIN' THEN 'TEAM_ADMIN'
            WHEN u.role = 'BASIC' THEN 'BASIC'
            WHEN u.role = 'CURATOR' THEN 'BASIC'
            WHEN u.role = 'GLOBAL_CURATOR' THEN 'BASIC'
            ELSE 'BASIC'
        END,
        is_admin = false  -- Only global admins get is_admin=true, not team admins
        FROM "user" u
        WHERE user__user_group.user_id = u.id
        AND user__user_group.user_group_id > 0
        AND user__user_group.role IS NULL
    """)
    
    # Step 5: Make role column NOT NULL now that all data is populated
    op.alter_column("user__user_group", "role", nullable=False)


def downgrade() -> None:
    # Make role column nullable again
    op.alter_column("user__user_group", "role", nullable=True)
    
    # Clear the new columns data
    op.execute('UPDATE "user" SET current_team_id = NULL, is_admin = false')
    op.execute('UPDATE user__user_group SET role = NULL, is_admin = false')
    
    # Remove admin users from the special admin group
    op.execute('DELETE FROM user__user_group WHERE user_group_id = -1')
    
    # Remove the special admin group
    op.execute('DELETE FROM user_group WHERE id = -1')
